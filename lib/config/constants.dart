import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter/foundation.dart';

// Pin filter types for map screens
enum PinFilterType { all, fresh, friends }

class AppConstants {
  // Server API endpoints
  static String get baseApiUrl {
    // Make this resilient to missing dotenv by providing a fallback
    try {
      return dotenv.env['API_BASE_URL'] ??
          'https://api.bopmaps.com/api';
    } catch (e) {
      // If dotenv is not initialized, return default
      return 'https://api.bopmaps.com/api';
    }
  }

  // Main API sections
  static const String usersAuthBase = '/auth';
  static const String musicBase = '/music';
  static const String pinsBase = '/pins';
  static const String profilesBase = '/profiles';
  static const String friendsBase = '/friends';

  // For backwards compatibility with existing code
  static const String authEndpoint = '/auth';
  static const String usersEndpoint = '/users';
  static const String pinsEndpoint = '/pins';

  // Friends endpoints
  static const String allFriendsEndpoint = '$friendsBase/all/';
  static const String friendRequestsEndpoint = '$friendsBase/requests/';
  static const String friendRequestsReceivedEndpoint =
      '$friendsBase/requests/received/';
  static const String friendRequestsSentEndpoint =
      '$friendsBase/requests/sent/';
  static const String friendSearchEndpoint = '$friendsBase/search/';

  // Authentication endpoints
  static const String loginEndpoint = '$usersAuthBase/token/';
  static const String refreshEndpoint = '$usersAuthBase/token/refresh/';
  static const String logoutEndpoint = '$usersAuthBase/logout/';
  static const String registerEndpoint = '$usersAuthBase/register/';
  static const String verifyEndpoint = '$usersAuthBase/verify/';
  static const String changePasswordEndpoint =
      '$usersAuthBase/change-password/';
  static const String resetPasswordEndpoint = '$usersAuthBase/reset-password/';
  static const String verifyEmailEndpoint = '$usersAuthBase/verify-email/';
  static const String connectMusicServiceEndpoint =
      '$usersAuthBase/connect-music-service/';

  // User endpoints
  static const String userProfileEndpoint = '$usersEndpoint/me/';
  static const String userSettingsEndpoint = '$usersEndpoint/settings/';
  static const String musicPreferencesEndpoint =
      '$usersEndpoint/music-preferences/';

  // Django Music service endpoints
  static const String spotifyAuthEndpoint = '$musicBase/auth/spotify/';
  static const String spotifyCallbackEndpoint =
      '$musicBase/auth/spotify/callback/';
  static const String spotifyTokenEndpoint = '$musicBase/auth/spotify/token/';
  static const String spotifyUserProfileEndpoint =
      '$musicBase/auth/spotify/profile/';
  static const String spotifyCallbackHandlerEndpoint =
      '$musicBase/auth/callback/';
  static const String spotifyRefreshEndpoint =
      '$musicBase/auth/spotify/refresh/';
  static const String connectedServicesEndpoint =
      '$musicBase/connected_services/';
  static const String appleMusicAuthEndpoint = '$musicBase/auth/apple/';
  static const String appleMusicUserTokenSyncEndpoint =
      '$musicBase/auth/apple/sync-token/';

  // Pin endpoints
  static const String allPinsEndpoint = '$pinsBase/';
  static const String nearbyPinsEndpoint = '$pinsBase/nearby/';
  static const String userPinsEndpoint = '$pinsBase/user/';
  static const String createPinEndpoint = '$pinsBase/create/';

  // Map settings
  static const double defaultZoom = 15.0;
  static const double maxZoom = 20.0;
  static const double minZoom = 10.0;
  static const double defaultPitch = 45.0; // For 3D-like effect

  // Default location (San Francisco) if user location is not available
  static const double defaultLatitude = 37.7749;
  static const double defaultLongitude = -122.4194;

  // Pin settings
  static const double pinDiscoveryRadius = 5000.0; // in meters
  static const Map<String, double> pinSizeByRarity = {
    'common': 60.0,
    'uncommon': 70.0,
    'rare': 80.0,
    'epic': 90.0,
    'legendary': 100.0,
  };

  // Authentication
  static const String tokenKey = 'auth_token';
  static const String refreshTokenKey = 'refresh_token';
  static const int tokenExpiryDays = 30;

  // Media
  static const int maxAudioPreviewDuration = 30; // in seconds
  static const int maxPinDescriptionLength = 200; // characters

  // ===== SPOTIFY API CONFIGURATION =====
  // Spotify API Constants
  static const String spotifyApiBaseUrl = 'https://api.spotify.com/v1';
  static const String spotifyAuthBaseUrl = 'https://accounts.spotify.com';
  static const String spotifyAuthUrl = '$spotifyAuthBaseUrl/authorize';
  static const String spotifyTokenUrl = '$spotifyAuthBaseUrl/api/token';

  static String get spotifyClientId {
    try {
      return dotenv.env['SPOTIFY_CLIENT_ID'] ?? 'placeholder_spotify_client_id';
    } catch (e) {
      return 'placeholder_spotify_client_id';
    }
  }

  static String get spotifyClientSecret {
    try {
      return dotenv.env['SPOTIFY_CLIENT_SECRET'] ??
          'placeholder_spotify_client_secret';
    } catch (e) {
      return 'placeholder_spotify_client_secret';
    }
  }

  static String get spotifyRedirectUri {
    // Always use the default for mobile
    return 'bopmaps://callback';
  }

  static List<String> get spotifyScopes => [
        'user-read-private',
        'user-read-email',
        'user-library-read',
        'user-library-modify',
        'user-read-recently-played',
        'user-top-read',
        'streaming',
      ];

  // ===== APPLE MUSIC API CONFIGURATION =====
  // Apple Music API Constants
  static const String appleMusicApiBaseUrl = 'https://api.music.apple.com/v1';

  static String get appleMusicDeveloperToken {
    try {
      final token = dotenv.env['APPLE_MUSIC_DEVELOPER_TOKEN'];
      if (token == null ||
          token.isEmpty ||
          token == 'placeholder_apple_music_developer_token') {
        print('⚠️ APPLE MUSIC SETUP REQUIRED:');
        print(
            '   1. Get your Apple Music developer token from https://developer.apple.com/account/');
        print(
            '   2. Add APPLE_MUSIC_DEVELOPER_TOKEN=your_token_here to your .env file');
        print('   3. Restart the app');
        print(
            '   4. Without a valid token, Apple Music features will use demo mode');
        return 'placeholder_apple_music_developer_token';
      }
      return token;
    } catch (e) {
      print('⚠️ Error reading Apple Music developer token from .env: $e');
      return 'placeholder_apple_music_developer_token';
    }
  }

  static String get appleMusicKeyId {
    try {
      return dotenv.env['APPLE_MUSIC_KEY_ID'] ??
          'placeholder_apple_music_key_id';
    } catch (e) {
      return 'placeholder_apple_music_key_id';
    }
  }

  static String get appleMusicTeamId {
    try {
      return dotenv.env['APPLE_MUSIC_TEAM_ID'] ??
          'placeholder_apple_music_team_id';
    } catch (e) {
      return 'placeholder_apple_music_team_id';
    }
  }

  static String get appleMusicPrivateKey {
    try {
      return dotenv.env['APPLE_MUSIC_PRIVATE_KEY'] ??
          'placeholder_apple_music_private_key';
    } catch (e) {
      return 'placeholder_apple_music_private_key';
    }
  }

  // ===== YOUTUBE API CONFIGURATION =====
  // YouTube Data API v3 Constants
  static const String youTubeApiBaseUrl =
      'https://www.googleapis.com/youtube/v3';

  static String get youTubeApiKey {
    try {
      return dotenv.env['YOUTUBE_API_KEY'] ?? 'placeholder_youtube_api_key';
    } catch (e) {
      return 'placeholder_youtube_api_key';
    }
  }

  // YouTube API configuration helper
  static bool get isYouTubeApiConfigured {
    return youTubeApiKey != 'placeholder_youtube_api_key';
  }

  // Mapbox
  static String get mapboxAccessToken {
    try {
      return dotenv.env['MAPBOX_ACCESS_TOKEN'] ?? 'placeholder_mapbox_token';
    } catch (e) {
      return 'placeholder_mapbox_token';
    }
  }

  static const String mapboxStyleUrl = 'mapbox://styles/mapbox/dark-v10';

  // Animation durations
  static const Duration pinAnimationDuration = Duration(milliseconds: 800);
  static const Duration mapTransitionDuration = Duration(milliseconds: 100);
  static const Duration auraEffectDuration = Duration(milliseconds: 1500);

  // Map Caching Configuration
  static const String geoBase = '/geo';
  static const String tilesBase = '$geoBase/tiles';
  static const String osmTilesEndpoint = '$tilesBase/osm';
  static const String vectorDataEndpoint = '$geoBase/buildings';
  static const String regionBundleEndpoint = '$geoBase/regions/bundle';
  static const String cacheStatsEndpoint = '$geoBase/cache/stats';

  // Cache Settings
  static const Duration tileCacheDuration =
      Duration(days: 30); // Matches Cache-Control header
  static const int maxCachedZoomLevel = 18; // Maximum zoom level for caching
  static const int minCachedZoomLevel = 10; // Minimum zoom level for caching
  static const int maxBatchSize = 50; // For bulk tile requests
  static const int maxCacheSizeBytes = 100 * 1024 * 1024; // 100MB
  static const String cacheVersion = '1.0.0';

  // Rate Limiting
  static const int maxRequestsPerMinute = 100;
  static const int maxVectorRequestsPerMinute = 50;
  static const int maxBundleRequestsPerHour = 5;

  // Error Codes
  static const String errorInvalidBounds = 'INVALID_BOUNDS';
  static const String errorRateLimitExceeded = 'RATE_LIMIT_EXCEEDED';
  static const String errorNetworkFailure = 'NETWORK_FAILURE';

  // WebSocket Configuration
  // static const String webSocketUrl = 'ws://api.bopmaps.com/ws/cache/';
  static const String webSocketUrl =
      'ws://api.bopmaps.com/ws/cache/';
  // SheerID API Configuration
  static String get sheerIdApiKey {
    try {
      return dotenv.env['SHEER_ID_API_KEY'] ?? 'placeholder_sheer_id_api_key';
    } catch (e) {
      return 'placeholder_sheer_id_api_key';
    }
  }

  static String get sheerIdStudentProgramId {
    try {
      return dotenv.env['SHEER_ID_STUDENT_PROGRAM_ID'] ??
          'placeholder_student_program_id';
    } catch (e) {
      return 'placeholder_student_program_id';
    }
  }

  static String get sheerIdProfessionalProgramId {
    try {
      return dotenv.env['SHEER_ID_PROFESSIONAL_PROGRAM_ID'] ??
          'placeholder_professional_program_id';
    } catch (e) {
      return 'placeholder_professional_program_id';
    }
  }

  // ===== CLOUDINARY API CONFIGURATION =====
  // Cloudinary API Constants
  static String get cloudinaryCloudName {
    try {
      return dotenv.env['CLOUDINARY_CLOUD_NAME'] ??
          'placeholder_cloudinary_cloud_name';
    } catch (e) {
      return 'placeholder_cloudinary_cloud_name';
    }
  }

  static String get cloudinaryApiKey {
    try {
      return dotenv.env['CLOUDINARY_API_KEY'] ??
          'placeholder_cloudinary_api_key';
    } catch (e) {
      return 'placeholder_cloudinary_api_key';
    }
  }

  static String get cloudinaryApiSecret {
    try {
      return dotenv.env['CLOUDINARY_API_SECRET'] ??
          'placeholder_cloudinary_api_secret';
    } catch (e) {
      return 'placeholder_cloudinary_api_secret';
    }
  }

  static String get cloudinaryUploadPreset {
    try {
      return dotenv.env['CLOUDINARY_UPLOAD_PRESET'] ??
          'placeholder_cloudinary_upload_preset';
    } catch (e) {
      return 'placeholder_cloudinary_upload_preset';
    }
  }

  // Cloudinary configuration helpers
  static bool get isCloudinaryConfigured {
    return cloudinaryCloudName != 'placeholder_cloudinary_cloud_name' &&
        cloudinaryApiKey != 'placeholder_cloudinary_api_key' &&
        cloudinaryApiSecret != 'placeholder_cloudinary_api_secret';
  }

  // ===== ONESIGNAL API CONFIGURATION =====
  // OneSignal API Constants
  static String get oneSignalAppId {
    try {
      return dotenv.env['ONESIGNAL_APP_ID'] ?? 'placeholder_onesignal_app_id';
    } catch (e) {
      return 'placeholder_onesignal_app_id';
    }
  }

  // OneSignal configuration helper
  static bool get isOneSignalConfigured {
    return oneSignalAppId != 'placeholder_onesignal_app_id';
  }

  // Stadia Maps API Key
  static String get stadiaApiKey {
    try {
      return dotenv.env['STADIA_API_KEY'] ??
          '7716731f-382e-4635-b070-61dd43a00e5a';
    } catch (e) {
      return '7716731f-382e-4635-b070-61dd43a00e5a';
    }
  }

  // ===== WEATHER API CONFIGURATION =====
  // WeatherAPI.com API Key
  static String get weatherApiKey {
    try {
      return dotenv.env['WEATHER_API_KEY'] ?? 'placeholder_weather_api_key';
    } catch (e) {
      return 'placeholder_weather_api_key';
    }
  }

  // Weather API configuration helper
  static bool get isWeatherApiConfigured {
    return weatherApiKey != 'placeholder_weather_api_key';
  }

  // ===== GOOGLE SIGN-IN CONFIGURATION =====
  static String get googleClientId {
    try {
      return dotenv.env['GOOGLE_CLIENT_ID'] ?? 'placeholder_google_client_id';
    } catch (e) {
      return 'placeholder_google_client_id';
    }
  }

  static String get googleServerClientId {
    try {
      return dotenv.env['GOOGLE_SERVER_CLIENT_ID'] ??
          'placeholder_google_server_client_id';
    } catch (e) {
      return 'placeholder_google_server_client_id';
    }
  }

  static bool get isGoogleSignInConfigured {
    return googleClientId != 'placeholder_google_client_id' &&
        googleServerClientId != 'placeholder_google_server_client_id';
  }
}
